# 微博热搜爬虫 - Python版本

## 功能对比

### 基础版本 (`weibo_spider.py`)
- ✅ 抓取热搜标题和链接
- ✅ 保存格式化的JSON数据
- ✅ 生成Markdown归档
- ✅ 更新README文件

### 增强版本 (`weibo_spider_enhanced.py`)
- ✅ 抓取热搜标题和链接
- ✅ **获取每个热搜的详细内容**
- ✅ **抓取相关热门微博**
- ✅ 保存详细的JSON数据
- ✅ 生成包含详细内容的Markdown文件
- ✅ 智能内容解析和清理

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests beautifulsoup4 lxml
```

## 使用方法

### 基础版本（只获取标题）
```bash
python weibo_spider.py
```

### 增强版本（获取详细内容）
```bash
python weibo_spider_enhanced.py
```

## 输出文件说明

### 基础版本输出
- `raw/yyyy-mm-dd.json` - 格式化的热搜列表JSON
- `archives/yyyy-mm-dd.md` - 每日归档Markdown
- `README.md` - 更新的主页面

### 增强版本输出
- `content/yyyy-mm-dd-enhanced.json` - 包含详细内容的JSON
- `content/yyyy-mm-dd-detailed.md` - 详细内容的Markdown文件

## 增强版本数据结构

```json
{
  "title": "热搜标题",
  "url": "/weibo?q=...",
  "full_url": "https://s.weibo.com/weibo?q=...",
  "content_fetched": true,
  "posts_count": 3,
  "hot_posts": [
    {
      "user": "用户名",
      "content": "微博内容...",
      "time": "发布时间",
      "index": 1
    }
  ]
}
```

## 特性说明

### 🚀 智能内容抓取
- 自动解析微博搜索结果页面
- 提取热门微博的用户、内容、时间信息
- 智能过滤无效内容

### ⚡ 性能优化
- 使用Session保持连接
- 添加请求延时避免被限制
- 可配置抓取数量（默认前5个热搜）

### 🛡️ 错误处理
- 网络异常自动重试
- 解析失败优雅降级
- 详细的错误日志

### 📊 数据格式
- JSON数据格式化美观
- Markdown文件结构清晰
- 支持中文编码

## 配置选项

在 `weibo_spider_enhanced.py` 的 `__main__` 部分可以调整：

```python
spider.run(
    fetch_content=True,      # 是否获取详细内容
    max_content_topics=5     # 获取详细内容的热搜数量
)
```

## 注意事项

1. **请求频率**: 增强版本会发送更多请求，建议适当控制频率
2. **运行时间**: 获取详细内容需要较长时间，请耐心等待
3. **网络环境**: 需要能够访问微博网站
4. **Cookie更新**: 如果长时间无法获取数据，可能需要更新Cookie

## 示例输出

### 控制台输出
```
🚀 微博热搜增强版爬虫启动
============================================================
🔍 正在抓取微博热搜列表...
✅ 成功抓取到 50 条热搜列表

🚀 开始获取前 5 个热搜的详细内容...
⚠️  注意：获取详细内容需要较长时间，请耐心等待...

📊 进度: 1/5
📖 正在获取 '华策发声明' 的详细内容...
✅ 成功获取 3 条相关微博

📊 进度: 2/5
📖 正在获取 '恋与深空严正声明' 的详细内容...
✅ 成功获取 2 条相关微博

...

✅ 爬虫运行完成！
📊 热搜总数: 50
📖 获取详细内容: 5 条
💬 相关微博总数: 12 条
📁 详细数据: content/2025-07-06-enhanced.json
📁 Markdown: content/2025-07-06-detailed.md
```

## 故障排除

### 常见问题

1. **无法获取数据**
   - 检查网络连接
   - 更新Cookie值
   - 检查微博网站是否可访问

2. **解析失败**
   - 微博页面结构可能发生变化
   - 需要更新解析规则

3. **请求被限制**
   - 增加请求间隔时间
   - 减少并发请求数量

### 调试模式

可以在代码中添加更多调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
