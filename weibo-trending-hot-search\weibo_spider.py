#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博热搜爬虫 - Python版本
功能：抓取微博热搜榜数据并保存为格式化的JSON和Markdown文件
"""

import requests
import json
import re
import os
from datetime import datetime
from typing import List, Dict
import urllib.parse


class WeiboSpider:
    def __init__(self):
        self.base_url = "https://s.weibo.com/top/summary"
        self.headers = {
            "Cookie": "SUB=_2AkMWJrkXf8NxqwJRmP8SxWjnaY12zwnEieKgekjMJRMxHRl-yj9jqmtbtRB6PaaX-IGp-AjmO6k5cS-OH2X9CayaTzVD",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.pattern = re.compile(r'<a href="(\/weibo\?q=[^"]+)".*?>(.+)<\/a>')
        
        # 确保目录存在
        os.makedirs("raw", exist_ok=True)
        os.makedirs("archives", exist_ok=True)
    
    def fetch_hot_search(self) -> List[Dict[str, str]]:
        """抓取微博热搜数据"""
        try:
            print("🔍 正在抓取微博热搜数据...")
            response = requests.get(self.base_url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            # 解析HTML内容
            matches = self.pattern.findall(response.text)
            
            words = []
            for url, title in matches:
                # 清理标题中的HTML标签
                clean_title = re.sub(r'<[^>]+>', '', title)
                words.append({
                    "title": clean_title,
                    "url": url,
                    "full_url": f"https://s.weibo.com{url}"
                })
            
            print(f"✅ 成功抓取到 {len(words)} 条热搜数据")
            return words
            
        except requests.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return []
        except Exception as e:
            print(f"❌ 解析数据失败: {e}")
            return []
    
    def load_existing_data(self, file_path: str) -> List[Dict[str, str]]:
        """加载已存在的数据"""
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def merge_words(self, new_words: List[Dict[str, str]], existing_words: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """合并新旧数据，去重"""
        # 使用字典去重，新数据覆盖旧数据
        merged = {}
        
        # 先添加旧数据
        for word in existing_words:
            merged[word['title']] = word
        
        # 再添加新数据（会覆盖同名的旧数据）
        for word in new_words:
            merged[word['title']] = word
        
        return list(merged.values())
    
    def save_json_data(self, words: List[Dict[str, str]], date_str: str):
        """保存JSON数据（格式化）"""
        file_path = os.path.join("raw", f"{date_str}.json")
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(words, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            print(f"💾 JSON数据已保存到: {file_path}")
        except Exception as e:
            print(f"❌ 保存JSON文件失败: {e}")
    
    def create_markdown_list(self, words: List[Dict[str, str]]) -> str:
        """创建Markdown格式的热搜列表"""
        current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
        
        lines = [
            "<!-- BEGIN -->",
            f"<!-- 最后更新时间: {current_time} -->",
            ""
        ]
        
        for i, word in enumerate(words, 1):
            # URL解码显示更友好的链接
            decoded_url = urllib.parse.unquote(word['url'])
            lines.append(f"{i}. [{word['title']}]({word['full_url']})")
        
        lines.extend(["", "<!-- END -->"])
        return "\n".join(lines)
    
    def update_readme(self, words: List[Dict[str, str]]):
        """更新README.md文件"""
        readme_path = "README.md"
        
        try:
            # 读取现有README
            if os.path.exists(readme_path):
                with open(readme_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            else:
                content = self.create_default_readme()
            
            # 替换热搜列表部分
            new_list = self.create_markdown_list(words)
            pattern = re.compile(r'<!-- BEGIN -->.*?<!-- END -->', re.DOTALL)
            updated_content = pattern.sub(new_list, content)
            
            # 保存更新后的README
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"📝 README.md 已更新")
            
        except Exception as e:
            print(f"❌ 更新README失败: {e}")
    
    def create_default_readme(self) -> str:
        """创建默认的README模板"""
        return """# 微博热搜榜 - Python版本

微博热搜榜数据抓取工具，每次运行都会获取最新的热搜数据。

## 今日热门搜索

<!-- BEGIN -->
<!-- 最后更新时间: 待更新 -->

<!-- END -->

## 使用方法

```bash
python weibo_spider.py
```

## 数据存储

- 原始数据: `raw/yyyy-mm-dd.json`
- 每日归档: `archives/yyyy-mm-dd.md`
- 主页显示: `README.md`
"""
    
    def create_archive(self, words: List[Dict[str, str]], date_str: str):
        """创建每日归档文件"""
        archive_path = os.path.join("archives", f"{date_str}.md")
        
        try:
            content = [
                f"# {date_str}",
                "",
                f"共 {len(words)} 条热搜",
                "",
                self.create_markdown_list(words)
            ]
            
            with open(archive_path, 'w', encoding='utf-8') as f:
                f.write("\n".join(content))
            
            print(f"📚 归档文件已保存到: {archive_path}")
            
        except Exception as e:
            print(f"❌ 创建归档失败: {e}")
    
    def display_preview(self, words: List[Dict[str, str]], limit: int = 10):
        """显示热搜预览"""
        print(f"\n🔥 今日热搜前{min(limit, len(words))}条:")
        print("-" * 50)
        
        for i, word in enumerate(words[:limit], 1):
            print(f"{i:2d}. {word['title']}")
        
        if len(words) > limit:
            print(f"... 还有 {len(words) - limit} 条")
        print("-" * 50)
    
    def run(self):
        """运行爬虫"""
        print("🚀 微博热搜爬虫启动")
        print("=" * 50)
        
        # 获取当前日期
        date_str = datetime.now().strftime("%Y-%m-%d")
        json_file = os.path.join("raw", f"{date_str}.json")
        
        # 抓取新数据
        new_words = self.fetch_hot_search()
        if not new_words:
            print("❌ 未能获取到数据，程序退出")
            return
        
        # 加载已有数据并合并
        existing_words = self.load_existing_data(json_file)
        merged_words = self.merge_words(new_words, existing_words)
        
        # 保存数据
        self.save_json_data(merged_words, date_str)
        self.update_readme(merged_words)
        self.create_archive(merged_words, date_str)
        
        # 显示预览
        self.display_preview(merged_words)
        
        print(f"\n✅ 爬虫运行完成！共处理 {len(merged_words)} 条数据")
        print(f"📁 数据文件: raw/{date_str}.json")
        print(f"📁 归档文件: archives/{date_str}.md")


if __name__ == "__main__":
    spider = WeiboSpider()
    spider.run()
