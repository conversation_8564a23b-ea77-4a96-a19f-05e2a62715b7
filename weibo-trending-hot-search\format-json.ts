#!/usr/bin/env -S deno run --allow-read --allow-write
// 美化JSON文件的工具脚本

import { join } from "std/path/mod.ts";

const args = Deno.args;
if (args.length === 0) {
  console.log("用法: deno run --allow-read --allow-write format-json.ts <日期>");
  console.log("例如: deno run --allow-read --allow-write format-json.ts 2025-07-06");
  Deno.exit(1);
}

const date = args[0];
const inputFile = join("raw", `${date}.json`);
const outputFile = join("raw", `${date}-formatted.json`);

try {
  // 读取原始JSON文件
  const content = await Deno.readTextFile(inputFile);
  const data = JSON.parse(content);
  
  // 格式化JSON并保存
  const formattedContent = JSON.stringify(data, null, 2);
  await Deno.writeTextFile(outputFile, formattedContent);
  
  console.log(`✅ 已创建格式化文件: ${outputFile}`);
  console.log(`📊 共 ${data.length} 条热搜数据`);
  
  // 显示前几条数据作为预览
  console.log("\n📝 前5条热搜预览:");
  data.slice(0, 5).forEach((item: any, index: number) => {
    console.log(`${index + 1}. ${item.title}`);
  });
  
} catch (error) {
  console.error("❌ 处理文件时出错:", error.message);
  Deno.exit(1);
}
