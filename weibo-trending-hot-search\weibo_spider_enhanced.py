#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博热搜爬虫 - 增强版本
功能：抓取微博热搜榜数据并获取每条热搜的详细内容
"""

import requests
import json
import re
import os
import time
from datetime import datetime
from typing import List, Dict, Optional
import urllib.parse
from bs4 import BeautifulSoup


class WeiboSpiderEnhanced:
    def __init__(self):
        self.base_url = "https://s.weibo.com/top/summary"
        self.search_base_url = "https://s.weibo.com"
        self.headers = {
            "Cookie": "SUB=_2AkMWJrkXf8NxqwJRmP8SxWjnaY12zwnEieKgekjMJRMxHRl-yj9jqmtbtRB6PaaX-IGp-AjmO6k5cS-OH2X9CayaTzVD",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        self.pattern = re.compile(r'<a href="(\/weibo\?q=[^"]+)".*?>(.+)<\/a>')
        
        # 确保目录存在
        os.makedirs("raw", exist_ok=True)
        os.makedirs("archives", exist_ok=True)
        os.makedirs("content", exist_ok=True)  # 存储详细内容
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def fetch_hot_search_list(self) -> List[Dict[str, str]]:
        """抓取微博热搜列表"""
        try:
            print("🔍 正在抓取微博热搜列表...")
            response = self.session.get(self.base_url, timeout=10)
            response.raise_for_status()
            
            # 解析HTML内容
            matches = self.pattern.findall(response.text)
            
            words = []
            for url, title in matches:
                # 清理标题中的HTML标签
                clean_title = re.sub(r'<[^>]+>', '', title)
                words.append({
                    "title": clean_title,
                    "url": url,
                    "full_url": f"https://s.weibo.com{url}",
                    "content": "",  # 详细内容，稍后填充
                    "hot_posts": []  # 热门微博，稍后填充
                })
            
            print(f"✅ 成功抓取到 {len(words)} 条热搜列表")
            return words
            
        except requests.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return []
        except Exception as e:
            print(f"❌ 解析数据失败: {e}")
            return []
    
    def fetch_topic_content(self, topic_url: str, title: str) -> Dict[str, any]:
        """获取单个热搜话题的详细内容"""
        try:
            print(f"📖 正在获取 '{title}' 的详细内容...")
            
            # 添加延时避免被限制
            time.sleep(1)
            
            response = self.session.get(f"{self.search_base_url}{topic_url}", timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取热门微博内容
            hot_posts = []
            
            # 查找微博卡片
            cards = soup.find_all('div', class_='card-wrap')
            if not cards:
                # 尝试其他可能的选择器
                cards = soup.find_all('div', {'class': re.compile(r'card|weibo|content')})
            
            for i, card in enumerate(cards[:5]):  # 只取前5条
                try:
                    # 提取微博文本内容
                    text_elem = card.find('p', class_='txt') or card.find('div', {'class': re.compile(r'text|content')})
                    if text_elem:
                        text = text_elem.get_text(strip=True)
                        if text and len(text) > 10:  # 过滤太短的内容
                            # 提取用户信息
                            user_elem = card.find('a', {'class': re.compile(r'name|user')})
                            user = user_elem.get_text(strip=True) if user_elem else "未知用户"
                            
                            # 提取时间信息
                            time_elem = card.find('a', {'class': re.compile(r'time|date')})
                            post_time = time_elem.get_text(strip=True) if time_elem else "未知时间"
                            
                            hot_posts.append({
                                "user": user,
                                "content": text[:500],  # 限制长度
                                "time": post_time,
                                "index": i + 1
                            })
                except Exception as e:
                    continue
            
            # 如果没有找到微博内容，尝试提取页面描述
            if not hot_posts:
                description = ""
                meta_desc = soup.find('meta', {'name': 'description'})
                if meta_desc:
                    description = meta_desc.get('content', '')
                
                # 或者提取页面标题相关信息
                page_title = soup.find('title')
                if page_title:
                    description = page_title.get_text(strip=True)
                
                if description:
                    hot_posts.append({
                        "user": "系统",
                        "content": description[:500],
                        "time": "未知",
                        "index": 1
                    })
            
            return {
                "success": True,
                "hot_posts": hot_posts,
                "total_found": len(hot_posts)
            }
            
        except requests.RequestException as e:
            print(f"❌ 获取 '{title}' 内容失败 (网络错误): {e}")
            return {"success": False, "error": str(e), "hot_posts": []}
        except Exception as e:
            print(f"❌ 解析 '{title}' 内容失败: {e}")
            return {"success": False, "error": str(e), "hot_posts": []}
    
    def fetch_all_content(self, words: List[Dict[str, str]], max_topics: int = 10) -> List[Dict[str, str]]:
        """获取所有热搜的详细内容"""
        print(f"\n🚀 开始获取前 {min(max_topics, len(words))} 个热搜的详细内容...")
        print("⚠️  注意：获取详细内容需要较长时间，请耐心等待...")
        
        enhanced_words = []
        
        for i, word in enumerate(words[:max_topics]):
            print(f"\n📊 进度: {i+1}/{min(max_topics, len(words))}")
            
            # 获取详细内容
            content_result = self.fetch_topic_content(word['url'], word['title'])
            
            # 更新数据
            enhanced_word = word.copy()
            enhanced_word['hot_posts'] = content_result['hot_posts']
            enhanced_word['content_fetched'] = content_result['success']
            enhanced_word['posts_count'] = len(content_result['hot_posts'])
            
            if content_result['success'] and content_result['hot_posts']:
                print(f"✅ 成功获取 {len(content_result['hot_posts'])} 条相关微博")
            else:
                print(f"⚠️  未能获取到详细内容")
            
            enhanced_words.append(enhanced_word)
        
        # 添加剩余的热搜（不获取详细内容）
        for word in words[max_topics:]:
            enhanced_word = word.copy()
            enhanced_word['hot_posts'] = []
            enhanced_word['content_fetched'] = False
            enhanced_word['posts_count'] = 0
            enhanced_words.append(enhanced_word)
        
        return enhanced_words
    
    def save_enhanced_json(self, words: List[Dict[str, str]], date_str: str):
        """保存增强版JSON数据"""
        file_path = os.path.join("content", f"{date_str}-enhanced.json")
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(words, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            print(f"💾 增强版数据已保存到: {file_path}")
        except Exception as e:
            print(f"❌ 保存增强版JSON失败: {e}")
    
    def create_enhanced_markdown(self, words: List[Dict[str, str]], date_str: str):
        """创建包含详细内容的Markdown文件"""
        file_path = os.path.join("content", f"{date_str}-detailed.md")
        
        try:
            lines = [
                f"# 微博热搜详细内容 - {date_str}",
                "",
                f"共 {len(words)} 条热搜，其中 {sum(1 for w in words if w.get('content_fetched', False))} 条获取了详细内容",
                "",
                "---",
                ""
            ]
            
            for i, word in enumerate(words, 1):
                lines.append(f"## {i}. {word['title']}")
                lines.append(f"**链接**: {word['full_url']}")
                lines.append("")
                
                if word.get('hot_posts') and len(word['hot_posts']) > 0:
                    lines.append("### 热门微博:")
                    lines.append("")
                    
                    for post in word['hot_posts']:
                        lines.append(f"**{post['user']}** ({post['time']}):")
                        lines.append(f"> {post['content']}")
                        lines.append("")
                else:
                    lines.append("*暂无详细内容*")
                    lines.append("")
                
                lines.append("---")
                lines.append("")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("\n".join(lines))
            
            print(f"📚 详细内容Markdown已保存到: {file_path}")
            
        except Exception as e:
            print(f"❌ 创建详细Markdown失败: {e}")
    
    def display_enhanced_preview(self, words: List[Dict[str, str]], limit: int = 5):
        """显示增强版预览"""
        print(f"\n🔥 热搜详细内容预览 (前{min(limit, len(words))}条):")
        print("=" * 80)
        
        for i, word in enumerate(words[:limit], 1):
            print(f"\n{i}. 【{word['title']}】")
            
            if word.get('hot_posts') and len(word['hot_posts']) > 0:
                print(f"   📊 找到 {len(word['hot_posts'])} 条相关微博")
                
                # 显示第一条微博内容
                first_post = word['hot_posts'][0]
                content_preview = first_post['content'][:100] + "..." if len(first_post['content']) > 100 else first_post['content']
                print(f"   💬 {first_post['user']}: {content_preview}")
            else:
                print("   ⚠️  暂无详细内容")
        
        print("=" * 80)
    
    def run(self, fetch_content: bool = True, max_content_topics: int = 10):
        """运行增强版爬虫"""
        print("🚀 微博热搜增强版爬虫启动")
        print("=" * 60)
        
        # 获取当前日期
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        # 1. 抓取热搜列表
        words = self.fetch_hot_search_list()
        if not words:
            print("❌ 未能获取到热搜列表，程序退出")
            return
        
        # 2. 获取详细内容（可选）
        if fetch_content:
            words = self.fetch_all_content(words, max_content_topics)
        
        # 3. 保存数据
        self.save_enhanced_json(words, date_str)
        self.create_enhanced_markdown(words, date_str)
        
        # 4. 显示预览
        self.display_enhanced_preview(words)
        
        # 5. 统计信息
        content_count = sum(1 for w in words if w.get('content_fetched', False))
        total_posts = sum(len(w.get('hot_posts', [])) for w in words)
        
        print(f"\n✅ 爬虫运行完成！")
        print(f"📊 热搜总数: {len(words)}")
        print(f"📖 获取详细内容: {content_count} 条")
        print(f"💬 相关微博总数: {total_posts} 条")
        print(f"📁 详细数据: content/{date_str}-enhanced.json")
        print(f"📁 Markdown: content/{date_str}-detailed.md")


if __name__ == "__main__":
    spider = WeiboSpiderEnhanced()
    
    # 运行爬虫
    # fetch_content=True: 获取详细内容
    # max_content_topics=10: 只获取前10个热搜的详细内容（避免时间过长）
    spider.run(fetch_content=True, max_content_topics=5)
