#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博热搜爬虫 - 修复版本
解决编码问题和内容获取问题
"""

import requests
import json
import re
import os
import time
import random
from datetime import datetime
from typing import List, Dict, Optional
import urllib.parse


class WeiboSpiderFixed:
    def __init__(self):
        self.base_url = "https://s.weibo.com/top/summary"
        self.headers = {
            "Cookie": "SUB=_2AkMWJrkXf8NxqwJRmP8SxWjnaY12zwnEieKgekjMJRMxHRl-yj9jqmtbtRB6PaaX-IGp-AjmO6k5cS-OH2X9CayaTzVD",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Referer": "https://weibo.com",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Upgrade-Insecure-Requests": "1"
        }
        self.pattern = re.compile(r'<a href="(\/weibo\?q=[^"]+)".*?>(.+)<\/a>')
        
        # 确保目录存在
        os.makedirs("raw", exist_ok=True)
        os.makedirs("archives", exist_ok=True)
        os.makedirs("content", exist_ok=True)
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def fetch_hot_search_list(self) -> List[Dict[str, str]]:
        """抓取微博热搜列表"""
        try:
            print("🔍 正在抓取微博热搜列表...")
            response = self.session.get(self.base_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'  # 确保正确编码
            
            # 解析HTML内容
            matches = self.pattern.findall(response.text)
            
            words = []
            for url, title in matches:
                # 清理标题中的HTML标签
                clean_title = re.sub(r'<[^>]+>', '', title)
                # 解码HTML实体
                clean_title = clean_title.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                
                words.append({
                    "title": clean_title,
                    "url": url,
                    "full_url": f"https://s.weibo.com{url}",
                    "summary": "",  # 简要描述
                    "related_info": []  # 相关信息
                })
            
            print(f"✅ 成功抓取到 {len(words)} 条热搜列表")
            return words
            
        except requests.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return []
        except Exception as e:
            print(f"❌ 解析数据失败: {e}")
            return []
    
    def get_topic_summary(self, title: str, url: str) -> Dict[str, str]:
        """根据热搜标题生成简要描述"""
        
        # 预定义的热搜类型和描述模板
        topic_patterns = {
            r'.*发声明.*|.*声明.*': "官方声明或回应相关事件",
            r'.*vs.*': "地区或事物对比讨论",
            r'.*演唱会.*|.*音乐.*': "娱乐演出相关话题",
            r'.*电影.*|.*电视剧.*|.*综艺.*': "影视娱乐内容",
            r'.*大学.*|.*学校.*|.*教育.*': "教育相关话题",
            r'.*明星.*|.*艺人.*|.*[演员|歌手].*': "明星艺人相关",
            r'.*政策.*|.*法律.*|.*规定.*': "政策法规相关",
            r'.*科技.*|.*AI.*|.*技术.*': "科技创新话题",
            r'.*健康.*|.*医疗.*|.*疫情.*': "健康医疗相关",
            r'.*经济.*|.*股市.*|.*金融.*': "经济金融话题",
            r'.*体育.*|.*比赛.*|.*运动.*': "体育赛事相关",
            r'.*天气.*|.*台风.*|.*地震.*': "天气自然灾害",
            r'.*美食.*|.*餐厅.*|.*菜.*': "美食餐饮话题",
            r'.*旅游.*|.*景点.*|.*出行.*': "旅游出行相关",
            r'.*房价.*|.*买房.*|.*租房.*': "房地产相关",
            r'.*工作.*|.*职场.*|.*就业.*': "职场就业话题"
        }
        
        # 根据标题匹配类型
        for pattern, description in topic_patterns.items():
            if re.search(pattern, title):
                return {
                    "type": "分类话题",
                    "summary": description,
                    "keywords": self.extract_keywords(title)
                }
        
        # 默认描述
        return {
            "type": "热门话题",
            "summary": f"关于'{title}'的热门讨论",
            "keywords": self.extract_keywords(title)
        }
    
    def extract_keywords(self, title: str) -> List[str]:
        """从标题中提取关键词"""
        # 移除常见的无意义词汇
        stop_words = {'的', '了', '在', '是', '有', '和', '与', '或', '但', '而', '因为', '所以', '如果', '那么', '这个', '那个', '一个', '什么', '怎么', '为什么'}
        
        # 简单的关键词提取（可以用更复杂的NLP方法）
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords[:5]  # 返回前5个关键词
    
    def enhance_with_summary(self, words: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """为热搜添加摘要信息"""
        print("\n📝 正在生成热搜摘要信息...")
        
        enhanced_words = []
        for i, word in enumerate(words):
            print(f"📊 处理进度: {i+1}/{len(words)} - {word['title']}")
            
            # 获取摘要信息
            summary_info = self.get_topic_summary(word['title'], word['url'])
            
            # 添加摘要信息
            enhanced_word = word.copy()
            enhanced_word.update({
                "topic_type": summary_info['type'],
                "summary": summary_info['summary'],
                "keywords": summary_info['keywords'],
                "analysis_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "rank": i + 1
            })
            
            enhanced_words.append(enhanced_word)
            
            # 添加小延时
            time.sleep(0.1)
        
        print("✅ 摘要信息生成完成")
        return enhanced_words
    
    def save_enhanced_data(self, words: List[Dict[str, str]], date_str: str):
        """保存增强数据"""
        # 保存JSON格式
        json_path = os.path.join("content", f"{date_str}-enhanced-fixed.json")
        try:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(words, f, ensure_ascii=False, indent=2, separators=(',', ': '))
            print(f"💾 增强数据已保存到: {json_path}")
        except Exception as e:
            print(f"❌ 保存JSON失败: {e}")
        
        # 保存可读性强的文本格式
        txt_path = os.path.join("content", f"{date_str}-readable.txt")
        try:
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"微博热搜榜 - {date_str}\n")
                f.write("=" * 50 + "\n\n")
                
                for word in words:
                    f.write(f"【第{word['rank']}名】{word['title']}\n")
                    f.write(f"类型: {word['topic_type']}\n")
                    f.write(f"描述: {word['summary']}\n")
                    f.write(f"关键词: {', '.join(word['keywords'])}\n")
                    f.write(f"链接: {word['full_url']}\n")
                    f.write("-" * 30 + "\n\n")
            
            print(f"📄 可读文本已保存到: {txt_path}")
        except Exception as e:
            print(f"❌ 保存文本失败: {e}")
    
    def display_preview(self, words: List[Dict[str, str]], limit: int = 10):
        """显示预览"""
        print(f"\n🔥 热搜榜预览 (前{min(limit, len(words))}名):")
        print("=" * 80)
        
        for word in words[:limit]:
            print(f"\n🏆 第{word['rank']}名: {word['title']}")
            print(f"   📂 类型: {word['topic_type']}")
            print(f"   📝 描述: {word['summary']}")
            print(f"   🏷️  关键词: {', '.join(word['keywords'])}")
        
        print("=" * 80)
    
    def run(self):
        """运行修复版爬虫"""
        print("🚀 微博热搜爬虫 - 修复版启动")
        print("=" * 60)
        
        # 获取当前日期
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        # 1. 抓取热搜列表
        words = self.fetch_hot_search_list()
        if not words:
            print("❌ 未能获取到热搜列表，程序退出")
            return
        
        # 2. 增强数据（添加摘要和分析）
        enhanced_words = self.enhance_with_summary(words)
        
        # 3. 保存数据
        self.save_enhanced_data(enhanced_words, date_str)
        
        # 4. 显示预览
        self.display_preview(enhanced_words)
        
        # 5. 统计信息
        print(f"\n✅ 爬虫运行完成！")
        print(f"📊 热搜总数: {len(enhanced_words)}")
        print(f"📁 数据文件: content/{date_str}-enhanced-fixed.json")
        print(f"📁 文本文件: content/{date_str}-readable.txt")
        print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    spider = WeiboSpiderFixed()
    spider.run()
